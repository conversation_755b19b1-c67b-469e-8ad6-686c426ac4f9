import matplotlib.pyplot as plt
import numpy as np
import torch as th
from diff_scm.datasets import loader
from diff_scm.utils import dist_util
from diff_scm.utils.script_util import get_models_from_config
from diff_scm.sampling.sampling_utils import get_models_functions, estimate_counterfactual
from diff_scm.configs import get_config

from diff_scm.configs import get_config

config = get_config.file_from_dataset("mnist")

# config = default_test_configs.get_default_configs()
dist_util.setup_dist()
print(f"device {dist_util.dev()}")
print(f"experiment folder {config.experiment_name}")
print(f"Counterfactual target class {config.sampling.target_class}")

test_loader = loader.get_data_loader("mnist", config, split_set='test', generator=False)

classifier, diffusion, model = get_models_from_config(config)
cond_fn, model_fn, model_classifier_free_fn, denoised_fn = get_models_functions(config, model, classifier)

data_dict = next(test_loader)

results_per_sample = {"original": ((data_dict['image'] + 1) * 127.5).clamp(0, 255).to(
    th.uint8)}
# send data points to GPU
model_kwargs = {k: v.to(dist_util.dev()) for k, v in data_dict.items()}
init_image = data_dict['image'].to(dist_util.dev())
# create counterfactual target 
model_kwargs["y"] = (config.sampling.target_class * th.ones((config.sampling.batch_size,))).to(th.long).to(dist_util.dev())

counterfactual, sampling_progression = estimate_counterfactual(config, 
                                                diffusion, cond_fn, model_fn, 
                                                model_classifier_free_fn, denoised_fn, 
                                                data_dict)

counterfactual = ((counterfactual + 1) * 127.5).clamp(0, 255).to(th.uint8)
results_per_sample["counterfactual"] = counterfactual.cpu().numpy()



sample_list = np.stack([v[:,0] for (k,v) in results_per_sample.items()])
sample_list = np.einsum("ibwh -> bihw",sample_list)
sample_list = sample_list[:16]
grid = np.concatenate(np.concatenate(sample_list,axis = 2),axis = 0)

fig = plt.figure(figsize=(12.,40.))
plt.imshow(grid,cmap = 'gray')
plt.axis("off")