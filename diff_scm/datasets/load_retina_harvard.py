import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import pandas as pd
from PIL import Image
from typing import Union, Dict, Any, Optional
from pathlib import Path
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec


class GlaucomaDataset(Dataset):
    """Glaucoma dataset with fundus images.
    
    Args:
        root (str or Path): Root directory of dataset
        train (bool): If True, creates dataset from training set, otherwise from test set
        resize (int): Size to resize images to
        normalize (bool): Whether to normalize images
    """
    
    def __init__(
        self,
        root: Union[str, Path],
        train: bool = True,
        resize: int = 256,
        normalize: bool = True,
        class_cond:bool=True
    ) -> None:
        # Define transforms
        if normalize:
            transform = transforms.Compose([
                transforms.Resize((resize, resize)),
                transforms.ToTensor(),
                transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5)),
            ])
        else:
            transform = transforms.Compose([
                transforms.Resize((resize, resize)),
                transforms.ToTensor(),
            ])
        
        self.root = Path(root)
        self.train = train
        self.transform = transform
        self.normalize = normalize
        self.class_cond = class_cond
        
        # Define class mapping
        self.class_to_idx = {
            'normal_control': 0,
            'early_glaucoma': 1,
            'advanced_glaucoma': 2
        }
        
        # Set the split directory
        split_dir = 'train' if train else 'test'
        self.data_dir = os.path.join(root, 'Glaucoma_fundus', split_dir)
        
        # Prepare data lists
        self.images = []
        self.labels = []
        
        # Load image paths and labels
        for class_name in self.class_to_idx:
            class_dir = os.path.join(self.data_dir, class_name)
            if os.path.isdir(class_dir):
                class_idx = self.class_to_idx[class_name]
                for img_name in os.listdir(class_dir):
                    if img_name.lower().endswith(('.png', '.jpg', '.jpeg')):
                        img_path = os.path.join(class_dir, img_name)
                        self.images.append(img_path)
                        self.labels.append(class_idx)
    
    def __getitem__(self, index: int):
        # Load image
        img_path = self.images[index]
        img = Image.open(img_path).convert('RGB')
        
        # Apply transformations
        img = self.transform(img)
        
        
        # Get label
        target = self.labels[index]

        if self.class_cond:
             return img, {"y": target}
        else:
            return img, {}
    
    def __len__(self) -> int:
        return len(self.images)


def display_samples(dataset, num_per_class=3):
    """Display samples from each class in the dataset."""
    # Create a figure with 3 rows (one per class) and num_per_class columns
    fig = plt.figure(figsize=(num_per_class * 4, 3 * 4))
    gs = GridSpec(3, num_per_class, figure=fig)
    
    # Get indices for each class
    class_indices = {0: [], 1: [], 2: []}
    for idx, (_, label_dict) in enumerate(dataset):
        label = label_dict["y"]
        if len(class_indices[label]) < num_per_class:
            class_indices[label].append(idx)
        
    for class_idx, indices in class_indices.items():
        for i, idx in enumerate(indices):
            img, label_dict = dataset[idx]
            
            img_path = dataset.images[idx]
            filename = os.path.basename(img_path)
            
            # Keep the normalized tensor as returned by __getitem__
            # Convert from tensor to numpy for display (keeping normalization)
            img_np = img.permute(1, 2, 0).numpy()
            
            ax = fig.add_subplot(gs[class_idx, i])
            ax.imshow(img_np)  
            ax.set_title(f"(Class {class_idx})\n{filename}", fontsize=9)
            ax.axis('off')
    
    plt.tight_layout()
    plt.savefig("glaucoma_samples_org.png")
    plt.show()


if __name__ == "__main__":
    # Create dataset
    dataset_root = "../../../datasets"  # Adjust path as needed
    train_dataset = GlaucomaDataset(root=dataset_root, train=True, normalize=False)
    
    # Print dataset information
    print(f"Dataset contains {len(train_dataset)} images")
    
    # Count samples per class
    class_counts = {0: 0, 1: 0, 2: 0}
    for _, label_dict in train_dataset:
        class_counts[label_dict["y"]] += 1
    
    print("Class distribution:")
    print(f"  Normal Control (0): {class_counts[0]} images")
    print(f"  Early Glaucoma (1): {class_counts[1]} images")
    print(f"  Advanced Glaucoma (2): {class_counts[2]} images")
    
    # Display samples
    display_samples(train_dataset)
