import torch
from torch.utils.data import Dataset
import torchvision.transforms as transforms
import os
from pathlib import Path
from PIL import Image
import sys

# Add the mnist module to the path
sys.path.append(str(Path(__file__).parent))
from mnist.io import load_idx

class MNIST_dataset(Dataset):
    def __init__(self, root_dir, train: bool = True):
        self.root_dir = Path(root_dir)
        self.train = train

        # Define transform to normalize to [-1, 1] range
        self.transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.5,), (0.5,)),
        ])

        # Load data using the IDX format loader
        self.images, self.labels = self._load_data()
        print(f"Loaded {'train' if train else 'test'} MNIST data: {len(self.images)} samples")

    def _load_data(self):
        """Load MNIST data from IDX files"""
        try:
            print(f"Looking for MNIST data in root_dir: {self.root_dir}")

            # Try to load from the mnist/raw directory first
            mnist_raw_dir = self.root_dir / "mnist" / "raw"
            print(f"Checking mnist/raw directory: {mnist_raw_dir}")
            print(f"mnist/raw exists: {mnist_raw_dir.exists()}")

            if mnist_raw_dir.exists():
                root_path = mnist_raw_dir
                print(f"Using mnist/raw directory: {root_path}")
            else:
                # Fallback to the provided root_dir
                root_path = self.root_dir
                print(f"Using fallback root_dir: {root_path}")

            image_file = f"{'train' if self.train else 't10k'}-images-idx3-ubyte"
            label_file = f"{'train' if self.train else 't10k'}-labels-idx1-ubyte"

            # Try with .gz extension first
            image_path = root_path / (image_file + ".gz")
            label_path = root_path / (label_file + ".gz")

            if not image_path.exists():
                image_path = root_path / image_file
                label_path = root_path / label_file

            if not image_path.exists():
                raise FileNotFoundError(f"Could not find MNIST data files in {root_path}")

            print(f"Loading images from: {image_path}")
            print(f"Loading labels from: {label_path}")

            images = load_idx(str(image_path))
            labels = load_idx(str(label_path))

            return images, labels

        except Exception as e:
            print(f"Error loading MNIST data: {e}")
            raise
    def __len__(self):
        return len(self.images)

    def __getitem__(self, idx):
        """
        Returns:
            dict: Dictionary with 'image' and 'y' keys, matching the expected format
        """
        # Get raw image and label
        img = self.images[idx]  # numpy array, shape (28, 28)
        target = int(self.labels[idx])

        # Convert to PIL Image and apply transform
        img = Image.fromarray(img, mode="L")
        img = self.transform(img)  # This will convert to tensor and normalize to [-1, 1]

        # Return in the expected format
        item = {
            'image': img,  # Tensor with shape (1, 28, 28) and values in [-1, 1]
            'y': target    # Integer label
        }
        return item