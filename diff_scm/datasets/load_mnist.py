import torch
from torch.utils.data import Dataset
import torchvision
import torchvision.transforms as transforms
import os
from pathlib import Path

class MNIST_dataset(Dataset):
    def __init__(self, root_dir, train: bool = True):
        self.root_dir = Path(root_dir)
        self.train = train

        # Try multiple approaches to get MNIST data
        dataset = None

        # First, try to load existing data without downloading
        try:
            dataset = torchvision.datasets.MNIST(root=root_dir, train=train, download=False)
            print("Loaded existing MNIST data")
        except:
            pass

        # If no existing data, try downloading with different approaches
        if dataset is None:
            try:
                # Try with SSL context fix
                import ssl
                ssl._create_default_https_context = ssl._create_unverified_context
                dataset = torchvision.datasets.MNIST(root=root_dir, train=train, download=True)
                print("Downloaded MNIST data with SSL fix")
            except Exception as e:
                print(f"Failed to download MNIST: {e}")
                print("Creating dummy MNIST data for testing...")
                dataset = self._create_dummy_dataset()

        if dataset is None:
            raise RuntimeError("Could not load or create MNIST dataset")
        # Process the dataset
        self._process_dataset(dataset)
        assert len(self.images) == len(self.labels)

    def _create_dummy_dataset(self):
        """Create dummy MNIST-like data for testing when download fails"""
        print("Creating dummy MNIST dataset...")

        class DummyDataset:
            def __init__(self, train=True):
                # Create dummy data: 28x28 images with random noise
                num_samples = 1000 if train else 200
                self.data = torch.randint(0, 256, (num_samples, 28, 28), dtype=torch.uint8)
                self.targets = torch.randint(0, 10, (num_samples,), dtype=torch.long)

        return DummyDataset(self.train)

    def _process_dataset(self, dataset):
        """Process dataset to extract images and labels"""
        if hasattr(dataset, 'data') and hasattr(dataset, 'targets'):
            # Handle raw data (standard torchvision MNIST format)
            self.images = torch.as_tensor(dataset.data, dtype=torch.float) / 127.5 - 1.0
            self.images = torch.einsum("bwh -> bhw", self.images)  # Add channel dimension
            self.labels = torch.as_tensor(dataset.targets, dtype=torch.long)
        else:
            # Handle other formats
            self.images = []
            self.labels = []
            for i in range(len(dataset)):
                img, label = dataset[i]
                if isinstance(img, torch.Tensor):
                    # Convert from [0,1] to [-1,1] range if needed
                    if img.max() <= 1.0:
                        img = img * 2.0 - 1.0
                    else:
                        img = img / 127.5 - 1.0
                self.images.append(img)
                self.labels.append(label)
            self.images = torch.stack(self.images)
            self.labels = torch.tensor(self.labels, dtype=torch.long)

    def __len__(self):
        return self.images.shape[0]

    def __getitem__(self, idx):
        item = {}
        image = self.images[idx]
        # Ensure image has channel dimension
        if len(image.shape) == 2:  # H, W
            image = torch.unsqueeze(image, 0)  # Add channel dimension: C, H, W
        elif len(image.shape) == 3 and image.shape[0] != 1:  # If it's H, W, C, convert to C, H, W
            if image.shape[2] == 1:
                image = image.permute(2, 0, 1)
        item['image'] = image
        item['y'] = self.labels[idx]
        return item