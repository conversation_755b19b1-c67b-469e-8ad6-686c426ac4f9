import torch
from torch.utils.data import Dataset
import torchvision
import torchvision.transforms as transforms

class MNIST_dataset(Dataset):
    def __init__(self, root_dir, train: bool = True):
        # Use transform to ensure we get the data in the right format
        transform = transforms.Compose([transforms.ToTensor()])
        try:
            dataset = torchvision.datasets.MNIST(root=root_dir, train=train, download=True, transform=transform)
        except Exception as e:
            print(f"Failed to download MNIST from default source: {e}")
            print("Trying alternative approach...")
            # Try without download first (in case data already exists)
            try:
                dataset = torchvision.datasets.MNIST(root=root_dir, train=train, download=False, transform=transform)
            except:
                # If that fails, try with a different mirror or approach
                import ssl
                ssl._create_default_https_context = ssl._create_unverified_context
                dataset = torchvision.datasets.MNIST(root=root_dir, train=train, download=True, transform=transform)
        ## From training set
        if hasattr(dataset, 'data') and hasattr(dataset, 'targets'):
            # Handle raw data (when transform is not applied to data attribute)
            self.images = torch.as_tensor(dataset.data, dtype=torch.float) / 127.5 - 1.0
            self.images = torch.einsum("bwh -> bhw", self.images)
            self.labels = torch.as_tensor(dataset.targets, dtype=torch.long)
        else:
            # Handle transformed data
            self.images = []
            self.labels = []
            for i in range(len(dataset)):
                img, label = dataset[i]
                # Convert from [0,1] to [-1,1] range
                img = img * 2.0 - 1.0
                self.images.append(img)
                self.labels.append(label)
            self.images = torch.stack(self.images)
            self.labels = torch.tensor(self.labels, dtype=torch.long)

        assert len(self.images) == len(self.labels)

    def __len__(self):
        return self.images.shape[0]

    def __getitem__(self, idx):
        item = {}
        image = self.images[idx]
        # Ensure image has channel dimension
        if len(image.shape) == 2:  # H, W
            image = torch.unsqueeze(image, 0)  # Add channel dimension: C, H, W
        elif len(image.shape) == 3 and image.shape[0] != 1:  # If it's H, W, C, convert to C, H, W
            if image.shape[2] == 1:
                image = image.permute(2, 0, 1)
        item['image'] = image
        item['y'] = self.labels[idx]
        return item