import torch
from torch.utils.data import Dataset
import torchvision.transforms as transforms
import os
from pathlib import Path
from PIL import Image
import sys

# Add the mnist module to the path
sys.path.append(str(Path(__file__).parent))
from mnist.io import load_idx

class MNIST_dataset(Dataset):
    def __init__(self, root_dir, train: bool = True):
        self.root_dir = Path(root_dir)
        self.train = train

        # Define transform to normalize to [-1, 1] range
        self.transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.5,), (0.5,)),
        ])

        # Load data using the IDX format loader
        self.images, self.labels = self._load_data()
        print(f"Loaded {'train' if train else 'test'} MNIST data: {len(self.images)} samples")

    def _load_data(self):
        """Load MNIST data from IDX files"""
        try:
            # Try to load from the mnist/raw directory first
            mnist_raw_dir = self.root_dir / "mnist" / "raw"
            if mnist_raw_dir.exists():
                root_path = mnist_raw_dir
            else:
                # Fallback to the provided root_dir
                root_path = self.root_dir

            image_file = f"{'train' if self.train else 't10k'}-images-idx3-ubyte"
            label_file = f"{'train' if self.train else 't10k'}-labels-idx1-ubyte"

            # Try with .gz extension first
            image_path = root_path / (image_file + ".gz")
            label_path = root_path / (label_file + ".gz")

            if not image_path.exists():
                image_path = root_path / image_file
                label_path = root_path / label_file

            if not image_path.exists():
                raise FileNotFoundError(f"Could not find MNIST data files in {root_path}")

            print(f"Loading images from: {image_path}")
            print(f"Loading labels from: {label_path}")

            images = load_idx(str(image_path))
            labels = load_idx(str(label_path))

            return images, labels

        except Exception as e:
            print(f"Error loading MNIST data: {e}")
            raise

    def _create_dummy_dataset(self):
        """Create dummy MNIST-like data for testing when download fails"""
        print("Creating dummy MNIST dataset...")

        class DummyDataset:
            def __init__(self, train=True):
                # Create dummy data: 28x28 images with random noise
                num_samples = 1000 if train else 200
                self.data = torch.randint(0, 256, (num_samples, 28, 28), dtype=torch.uint8)
                self.targets = torch.randint(0, 10, (num_samples,), dtype=torch.long)

        return DummyDataset(self.train)

    def _process_dataset(self, dataset):
        """Process dataset to extract images and labels"""
        if hasattr(dataset, 'data') and hasattr(dataset, 'targets'):
            # Handle raw data (standard torchvision MNIST format)
            self.images = torch.as_tensor(dataset.data, dtype=torch.float) / 127.5 - 1.0
            self.images = torch.einsum("bwh -> bhw", self.images)  # Add channel dimension
            self.labels = torch.as_tensor(dataset.targets, dtype=torch.long)
        else:
            # Handle other formats
            self.images = []
            self.labels = []
            for i in range(len(dataset)):
                img, label = dataset[i]
                if isinstance(img, torch.Tensor):
                    # Convert from [0,1] to [-1,1] range if needed
                    if img.max() <= 1.0:
                        img = img * 2.0 - 1.0
                    else:
                        img = img / 127.5 - 1.0
                self.images.append(img)
                self.labels.append(label)
            self.images = torch.stack(self.images)
            self.labels = torch.tensor(self.labels, dtype=torch.long)

    def __len__(self):
        return self.images.shape[0]

    def __getitem__(self, idx):
        item = {}
        image = self.images[idx]
        # Ensure image has channel dimension
        if len(image.shape) == 2:  # H, W
            image = torch.unsqueeze(image, 0)  # Add channel dimension: C, H, W
        elif len(image.shape) == 3 and image.shape[0] != 1:  # If it's H, W, C, convert to C, H, W
            if image.shape[2] == 1:
                image = image.permute(2, 0, 1)
        item['image'] = image
        item['y'] = self.labels[idx]
        return item