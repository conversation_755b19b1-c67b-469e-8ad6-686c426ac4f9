#!/usr/bin/env python3
"""
Generate counterfactual samples using Diff-SCM
Converted from examples/example_generate_counterfactual.ipynb
"""

import matplotlib.pyplot as plt
import numpy as np
import torch as th
from diff_scm.datasets import loader
from diff_scm.utils import dist_util
from diff_scm.utils.script_util import get_models_from_config
from diff_scm.sampling.sampling_utils import get_models_functions, estimate_counterfactual
from diff_scm.configs import get_config

def main():
    print("Loading configuration...")
    config = get_config.file_from_dataset("mnist")
    
    print("Setting up distributed training...")
    dist_util.setup_dist()
    print(f"Device: {dist_util.dev()}")
    print(f"Experiment folder: {config.experiment_name}")
    print(f"Counterfactual target class: {config.sampling.target_class}")
    
    print("Loading test data...")
    test_loader = loader.get_data_loader("mnist", config, split_set='test', generator=False)
    print(f"Test loader created with {len(test_loader)} batches")
    
    print("Loading models...")
    classifier, diffusion, model = get_models_from_config(config)
    print("Models loaded successfully")
    
    print("Getting model functions...")
    cond_fn, model_fn, model_classifier_free_fn, denoised_fn = get_models_functions(config, model, classifier)
    print("Model functions obtained")
    
    print("Getting data batch...")
    data_dict = next(iter(test_loader))
    print(f"Data batch shape: {data_dict['image'].shape}")
    print(f"Labels shape: {data_dict['y'].shape}")
    
    print("Preparing results dictionary...")
    results_per_sample = {"original": ((data_dict['image'] + 1) * 127.5).clamp(0, 255).to(th.uint8)}
    
    print("Sending data to GPU...")
    # Send data points to GPU
    model_kwargs = {k: v.to(dist_util.dev()) for k, v in data_dict.items()}
    init_image = data_dict['image'].to(dist_util.dev())
    
    print("Creating counterfactual target...")
    # Create counterfactual target 
    model_kwargs["y"] = (config.sampling.target_class * th.ones((config.sampling.batch_size,))).to(th.long).to(dist_util.dev())
    
    print("Generating counterfactuals...")
    counterfactual, sampling_progression = estimate_counterfactual(
        config, 
        diffusion, 
        cond_fn, 
        model_fn, 
        model_classifier_free_fn, 
        denoised_fn, 
        data_dict
    )
    
    print("Processing counterfactual results...")
    counterfactual = ((counterfactual + 1) * 127.5).clamp(0, 255).to(th.uint8)
    results_per_sample["counterfactual"] = counterfactual.cpu().numpy()
    
    print("Creating visualization grid...")
    sample_list = np.stack([v[:,0] for (k,v) in results_per_sample.items()])
    sample_list = np.einsum("ibwh -> bihw", sample_list)
    sample_list = sample_list[:16]
    grid = np.concatenate(np.concatenate(sample_list, axis=2), axis=0)
    
    print(f"Grid shape: {grid.shape}")
    
    print("Displaying results...")
    plt.figure(figsize=(12, 8))
    plt.imshow(grid, cmap='gray')
    plt.axis('off')
    plt.title('Original vs Counterfactual MNIST Samples')
    plt.tight_layout()
    
    # Save the plot
    output_file = 'counterfactual_results.png'
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    print(f"Results saved to {output_file}")
    
    # Also show the plot if running interactively
    plt.show()
    
    print("Counterfactual generation completed successfully!")

if __name__ == "__main__":
    main()
