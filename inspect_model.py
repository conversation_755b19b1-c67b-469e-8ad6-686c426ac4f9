#!/usr/bin/env python3
"""
Inspect the model091000.pt file to extract architecture parameters
"""

import torch
import json
from collections import defaultdict

def analyze_model_architecture(model_path):
    """Analyze the model checkpoint to infer architecture parameters"""
    
    print(f"Loading model from: {model_path}")
    checkpoint = torch.load(model_path, map_location='cpu')
    
    # Print basic info about the checkpoint
    print(f"Checkpoint keys: {list(checkpoint.keys())}")
    
    # Get the model state dict
    if 'model' in checkpoint:
        state_dict = checkpoint['model']
    elif 'state_dict' in checkpoint:
        state_dict = checkpoint['state_dict']
    else:
        state_dict = checkpoint
    
    print(f"\nTotal parameters: {len(state_dict)}")
    
    # Analyze layer dimensions to infer architecture
    architecture_info = {}
    
    # Look for key layers to determine architecture
    for key, tensor in state_dict.items():
        print(f"{key}: {tensor.shape}")
        
        # Analyze input blocks
        if 'input_blocks.0.0.weight' in key:
            # This should be the first conv layer
            in_channels = tensor.shape[1]
            out_channels = tensor.shape[0]
            architecture_info['input_channels'] = in_channels
            architecture_info['num_channels'] = out_channels
            
        # Analyze time embedding
        if 'time_embed.0.weight' in key:
            time_embed_dim = tensor.shape[0]
            architecture_info['time_embed_dim'] = time_embed_dim
            
        # Analyze output layer
        if 'out.2.weight' in key:
            final_channels = tensor.shape[1]
            output_channels = tensor.shape[0]
            architecture_info['final_channels'] = final_channels
            architecture_info['output_channels'] = output_channels
    
    # Infer channel multipliers by looking at different blocks
    channel_info = defaultdict(list)
    
    for key, tensor in state_dict.items():
        if 'input_blocks' in key and 'in_layers.2.weight' in key:
            # Extract block number and channel count
            block_num = int(key.split('.')[1])
            channels = tensor.shape[0]
            channel_info['input_blocks'].append((block_num, channels))
            
        if 'middle_block' in key and 'in_layers.2.weight' in key:
            channels = tensor.shape[0]
            channel_info['middle_block'].append(channels)
            
        if 'output_blocks' in key and 'in_layers.2.weight' in key:
            block_num = int(key.split('.')[1])
            channels = tensor.shape[0]
            channel_info['output_blocks'].append((block_num, channels))
    
    # Sort and analyze channel progression
    if channel_info['input_blocks']:
        input_channels = sorted(channel_info['input_blocks'])
        print(f"\nInput blocks channels: {input_channels}")
        
        # Infer channel multiplier
        base_channels = input_channels[0][1] if input_channels else 64
        multipliers = []
        for block_num, channels in input_channels:
            mult = channels // base_channels
            if mult not in multipliers:
                multipliers.append(mult)
        
        architecture_info['base_channels'] = base_channels
        architecture_info['channel_mult'] = tuple(multipliers)
    
    if channel_info['middle_block']:
        print(f"Middle block channels: {channel_info['middle_block']}")
        
    if channel_info['output_blocks']:
        output_channels = sorted(channel_info['output_blocks'])
        print(f"Output blocks channels: {output_channels}")
    
    # Count number of residual blocks per resolution
    res_block_counts = defaultdict(int)
    for key in state_dict.keys():
        if 'input_blocks' in key:
            parts = key.split('.')
            if len(parts) >= 3:
                block_num = int(parts[1])
                sub_block = int(parts[2])
                res_block_counts[f'input_{block_num}'] = max(res_block_counts[f'input_{block_num}'], sub_block + 1)
    
    # Estimate num_res_blocks (typically the same across all resolutions)
    if res_block_counts:
        typical_res_blocks = max(res_block_counts.values()) - 1  # Subtract 1 for downsample layer
        architecture_info['num_res_blocks'] = typical_res_blocks
    
    print(f"\nInferred architecture parameters:")
    for key, value in architecture_info.items():
        print(f"  {key}: {value}")
    
    return architecture_info

def generate_config_updates(architecture_info):
    """Generate the config updates needed"""
    
    print(f"\n" + "="*50)
    print("SUGGESTED CONFIG UPDATES:")
    print("="*50)
    
    if 'num_channels' in architecture_info:
        print(f"score_model.num_channels = {architecture_info['num_channels']}")
    
    if 'channel_mult' in architecture_info:
        print(f"score_model.channel_mult = {architecture_info['channel_mult']}")
    
    if 'num_res_blocks' in architecture_info:
        print(f"score_model.num_res_blocks = {architecture_info['num_res_blocks']}")
    
    if 'time_embed_dim' in architecture_info:
        model_channels = architecture_info.get('num_channels', 64)
        time_embed_mult = architecture_info['time_embed_dim'] // model_channels
        print(f"# Time embedding dimension: {architecture_info['time_embed_dim']} (mult: {time_embed_mult})")
    
    print("\nCopy these values to your mnist_configs.py file!")

if __name__ == "__main__":
    model_path = "./model091000.pt"
    
    try:
        architecture_info = analyze_model_architecture(model_path)
        generate_config_updates(architecture_info)
        
    except Exception as e:
        print(f"Error analyzing model: {e}")
        import traceback
        traceback.print_exc()
