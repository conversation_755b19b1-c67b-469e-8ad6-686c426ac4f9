name: diffscm_gpu
channels:
  - pytorch
  - anaconda
  - conda-forge
  - bioconda
  - defaults
dependencies:
  - _libgcc_mutex=0.1
  - absl-py=0.12.0
  - aiohttp=3.7.0
  - argon2-cffi=20.1.0
  - async-timeout=3.0.1
  - async_generator=1.10
  - attrs=20.3.0
  - backcall=0.2.0
  - beautifulsoup4=4.9.3
  - binutils_impl_linux-64=2.31.1
  - binutils_linux-64=2.31.1
  - blas=1.0
  - bleach=3.2.1
  - blinker=1.4
  - brotlipy=0.7.0
  - bzip2=1.0.8
  - c-ares=1.17.1
  - ca-certificates=2020.10.14
  - cachetools=4.2.1
  - cairo=1.14.12
  - certifi=2020.6.20
  - cffi=1.14.4
  - chardet=3.0.4
  - click=7.1.2
  - cloudpickle=1.6.0
  - conda=4.10.1
  - conda-build=3.21.4
  - conda-package-handling=1.7.3
  - contextlib2=0.6.0.post1
  - cryptography=3.2.1
  - cudatoolkit=11.0.221
  - cycler=0.10.0
  - cytoolz=0.11.0
  - dask-core=2.30.0
  - dbus=1.13.18
  - decorator=4.4.2
  - defusedxml=0.6.0
  - entrypoints=0.3
  - expat=2.3.0
  - filelock=3.0.12
  - fontconfig=2.13.1
  - freetype=2.10.4
  - fribidi=1.0.10
  - fsspec=0.8.7
  - future=0.18.2
  - gcc_impl_linux-64=7.3.0
  - gcc_linux-64=7.3.0
  - gfortran_impl_linux-64=7.3.0
  - gfortran_linux-64=7.3.0
  - glib=2.63.1
  - glob2=0.7
  - google-auth=1.28.0
  - google-auth-oauthlib=0.4.1
  - graphite2=1.3.14
  - graphviz=2.40.1
  - gst-plugins-base=1.14.0
  - gstreamer=1.14.0
  - gxx_impl_linux-64=7.3.0
  - gxx_linux-64=7.3.0
  - harfbuzz=1.8.8
  - icu=58.2
  - idna=2.10
  - imageio=2.9.0
  - importlib-metadata=3.10.0
  - importlib_metadata=1.5.0
  - iniconfig=1.1.1
  - intel-openmp=2020.2
  - ipykernel=5.3.4
  - ipython=7.18.1
  - ipython_genutils=0.2.0
  - ipywidgets=7.5.1
  - jax=0.2.12
  - jaxlib=0.1.65
  - jedi=0.18.0
  - jinja2=2.11.2
  - joblib=0.17.0
  - jpeg=9b
  - jsonschema=3.2.0
  - jupyter=1.0.0
  - jupyter_client=6.1.7
  - jupyter_console=6.2.0
  - jupyter_core=4.6.3
  - jupyterlab_pygments=0.1.2
  - kiwisolver=1.2.0
  - lcms2=2.11
  - ld_impl_linux-64=2.33.1
  - libarchive=3.4.2
  - libedit=3.1.20191231
  - libffi=3.2.1
  - libgcc-ng=9.1.0
  - libgfortran-ng=7.3.0
  - liblief=0.10.1
  - libpng=1.6.37
  - libprotobuf=********
  - libsodium=1.0.18
  - libstdcxx-ng=9.1.0
  - libtiff=4.1.0
  - libuuid=1.0.3
  - libuv=1.40.0
  - libxcb=1.14
  - libxml2=2.9.10
  - lz4-c=1.9.3
  - markdown=3.3.4
  - markupsafe=1.1.1
  - matplotlib=3.3.4
  - matplotlib-base=3.3.4
  - mistune=0.8.4
  - mkl=2020.2
  - mkl-service=2.3.0
  - mkl_fft=1.3.0
  - mkl_random=1.1.1
  - more-itertools=8.7.0
  - mpi=1.0
  - mpi4py=3.0.3
  - mpich=3.3.2
  - multidict=4.7.5
  - nbclient=0.5.1
  - nbconvert=6.0.7
  - nbformat=5.0.8
  - ncurses=6.2
  - nest-asyncio=1.4.1
  - networkx=2.5
  - ninja=1.10.2
  - notebook=6.1.4
  - numpy=1.19.2
  - numpy-base=1.19.2
  - oauthlib=3.0.1
  - olefile=0.46
  - openssl=1.1.1h
  - packaging=20.9
  - pandas=1.2.0
  - pandoc=2.11
  - pandocfilters=1.4.2
  - pango=1.42.4
  - parso=0.8.0
  - patchelf=0.12
  - pcre=8.44
  - pexpect=4.8.0
  - pickleshare=0.7.5
  - pillow=8.1.1
  - pip=21.0.1
  - pixman=0.40.0
  - pkginfo=1.7.0
  - pluggy=0.13.1
  - prometheus_client=0.8.0
  - prompt-toolkit=3.0.8
  - prompt_toolkit=3.0.8
  - protobuf=********
  - psutil=5.8.0
  - ptyprocess=0.6.0
  - py=1.10.0
  - py-lief=0.10.1
  - pyasn1=0.4.8
  - pyasn1-modules=0.2.7
  - pycosat=0.6.3
  - pycparser=2.20
  - pygments=2.7.1
  - pyjwt=2.0.1
  - pyopenssl=20.0.1
  - pyparsing=2.4.7
  - pyqt=5.9.2
  - pyrsistent=0.17.3
  - pysocks=1.7.1
  - pytest=6.2.3
  - python=3.8.2
  - python-dateutil=2.8.1
  - python-flatbuffers=1.12
  - python-graphviz=0.16
  - python-libarchive-c=2.9
  - python_abi=3.8
  - pytorch=1.7.1
  - pytorch-lightning=1.2.6
  - pytz=2021.1
  - pywavelets=1.1.1
  - pyyaml=5.3.1
  - pyzmq=19.0.2
  - qt=5.9.7
  - qtconsole=4.7.7
  - qtpy=1.9.0
  - readline=8.1
  - requests=2.25.1
  - requests-oauthlib=1.3.0
  - ripgrep=12.1.1
  - rsa=4.7.2
  - ruamel_yaml=0.15.100
  - scikit-image=0.17.2
  - scikit-learn=0.23.2
  - scipy=1.6.2
  - seaborn=0.11.1
  - send2trash=1.5.0
  - setuptools=52.0.0
  - sip=4.19.13
  - six=1.15.0
  - soupsieve=2.2.1
  - sqlite=3.33.0
  - tensorboard-plugin-wit=1.8.0
  - terminado=0.9.1
  - testpath=0.4.4
  - threadpoolctl=2.1.0
  - tifffile=2020.10.1
  - tk=8.6.10
  - toml=0.10.2
  - toolz=0.11.1
  - torchaudio=0.7.2
  - torchmetrics=0.2.0
  - torchvision=0.8.2
  - tornado=6.0.4
  - tqdm=4.59.0
  - traitlets=5.0.5
  - typing_extensions=*******
  - urllib3=1.26.4
  - wcwidth=0.2.5
  - webencodings=0.5.1
  - werkzeug=1.0.1
  - wheel=0.36.2
  - widgetsnbextension=3.5.1
  - xz=5.2.5
  - yaml=0.2.5
  - yarl=1.6.3
  - zeromq=4.3.3
  - zipp=3.4.1
  - zlib=1.2.11
  - zstd=1.4.5
  - pip:
    - blobfile==1.2.8
    - contextlib2==21.6.0
    - filelock==3.6.0
    - ml-collections==0.1.1
    - pycryptodomex==3.14.1
    - pyyaml==6.0
    - xmltodict==0.12.0